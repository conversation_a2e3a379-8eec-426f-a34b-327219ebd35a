@using DiagramEditor.Models
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Components.Renderers
@inherits ComponentBase

<div class="diamond-node" 
     style="width: @(Node.Size?.Width ?? 100)px; 
            height: @(Node.Size?.Height ?? 60)px; 
            background-color: @Node.BackgroundColor; 
            border: @(Node.BorderWidth)px solid @Node.BorderColor;
            color: @Node.TextColor;
            transform: rotate(45deg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;">
    
    <div style="transform: rotate(-45deg); text-align: center;">
        @if (!string.IsNullOrEmpty(Node.Title))
        {
            <span>@Node.Title</span>
        }
    </div>

    @foreach (var port in Node.Ports)
    {
        <PortRenderer @key="port" Port="port" Class="default"></PortRenderer>
    }
</div>

<style>
    .diamond-node:hover {
        transform: rotate(45deg) scale(1.05);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .diamond-node.selected {
        border-color: #FF5722 !important;
        border-width: 3px !important;
    }
</style>

@code {
    [Parameter] public DiamondNodeModel Node { get; set; } = null!;
}
