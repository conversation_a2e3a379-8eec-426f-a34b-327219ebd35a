{"Version": 1, "Hash": "6p9Wx9ZAYFLVT1DtHUfqmpkV5rJd7pbYFumnQQDIbss=", "Source": "DiagramEditor", "BasePath": "_content/DiagramEditor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "Version": 2, "Source": "Z.Blazor.Diagrams", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "TargetFramework=net9.0", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "TargetFramework=net9.0", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [{"Name": "DiagramEditor\\wwwroot", "Source": "DiagramEditor", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "Pattern": "**"}, {"Name": "Z.Blazor.Diagrams\\wwwroot", "Source": "Z.Blazor.Diagrams", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1xiwpnv0y9", "Integrity": "O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "FileLength": 508754, "LastWriteTime": "2023-12-18T22:14:46+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rftke82za3", "Integrity": "+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "FileLength": 44027, "LastWriteTime": "2023-12-18T22:14:48+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint=6pwzqlbbfs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhqk7gbln5", "Integrity": "7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "FileLength": 680, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint=c5cp0u3gkb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9r2jwh9rj", "Integrity": "g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "FileLength": 651, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint=kr4r5y5l5h}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "469wz5lhr2", "Integrity": "bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "FileLength": 838, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint=9j2o0uhpet}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yjpzo0a57p", "Integrity": "HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "FileLength": 510, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 93, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "FileLength": 418, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "FileLength": 520, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.<PERSON>.Diagrams#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "wxhkjam3jz", "Integrity": "1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 81, "LastWriteTime": "2025-07-10T01:46:41+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kr4r5y5l5h", "Integrity": "IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.css", "FileLength": 3445, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pwzqlbbfs", "Integrity": "tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.min.css", "FileLength": 2533, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c5cp0u3gkb", "Integrity": "QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.js", "FileLength": 2034, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u872bpsf3j", "Integrity": "LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.min.js", "FileLength": 1071, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9j2o0uhpet", "Integrity": "K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.css", "FileLength": 1939, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Project", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjpcwcpl0m", "Integrity": "UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.min.css", "FileLength": 1327, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-rftke82za3.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dplvskzkxa", "Integrity": "X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "FileLength": 9705, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint=cmapd0fi15}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qvz7hczgpc", "Integrity": "ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 2106, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint=1b9liozj85}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eopxey7q6y", "Integrity": "wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "FileLength": 943, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=bpk8xqwxhs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li2ohn2azb", "Integrity": "vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 23851, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-cp57924o9j.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/site#[.{fingerprint=cp57924o9j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdnnnfw5tu", "Integrity": "Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "FileLength": 2236, "LastWriteTime": "2025-07-10T02:17:13+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/diagram-export#[.{fingerprint=hz8bn5rrj8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "seii0zkrfo", "Integrity": "tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "FileLength": 1716, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/README#[.{fingerprint=5bzwdl5l6x}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jbzbw5vlik", "Integrity": "cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "FileLength": 1484, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/blazor-diagrams.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "FileLength": 520, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/responsive#[.{fingerprint=wkr7rr9ybj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3idrsuck7n", "Integrity": "5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "FileLength": 2913, "LastWriteTime": "2025-07-10T01:50:00+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/blazor-diagrams.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "FileLength": 418, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint=wk8x8xm0ah}]?.otf.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gzw6z059fx", "Integrity": "hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 13066, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-1xiwpnv0y9.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tvdvut284s", "Integrity": "9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "FileLength": 55077, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m532s4naxw", "Integrity": "epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 74642, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint=sjnzgf7e1h}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t673mz4ep7", "Integrity": "27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 13464, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint=v3ikh3owht}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l13wvbmc9f", "Integrity": "lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "FileLength": 979, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "v3ikh3owht", "Integrity": "VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "FileLength": 3102, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "1b9liozj85", "Integrity": "ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "FileLength": 3017, "LastWriteTime": "2025-07-10T01:46:43+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/blazor-diagrams.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjpcwcpl0m", "Integrity": "UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\blazor-diagrams.min.css", "FileLength": 1327, "LastWriteTime": "2025-07-10T01:43:17+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/FONT-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "48tmkg660f", "Integrity": "jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\FONT-LICENSE", "FileLength": 4103, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cmapd0fi15", "Integrity": "BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 9395, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0uw8dim9nl", "Integrity": "OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "FileLength": 28196, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wk8x8xm0ah", "Integrity": "sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 20996, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sjnzgf7e1h", "Integrity": "+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 55332, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ll5grcv8wv", "Integrity": "p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "FileLength": 28028, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h4d0pazwgy", "Integrity": "cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "FileLength": 14984, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/ICON-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4dwjve0o0b", "Integrity": "aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\ICON-LICENSE", "FileLength": 1093, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5bzwdl5l6x", "Integrity": "waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\README.md", "FileLength": 3655, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cp57924o9j", "Integrity": "YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 4929, "LastWriteTime": "2025-07-10T02:14:35+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\favicon.png", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-07-09T18:10:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/blazor-diagrams.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u872bpsf3j", "Integrity": "LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\blazor-diagrams.min.js", "FileLength": 1071, "LastWriteTime": "2025-07-10T01:43:09+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/diagram-export#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hz8bn5rrj8", "Integrity": "PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\diagram-export.js", "FileLength": 6217, "LastWriteTime": "2025-07-09T18:42:28+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/responsive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wkr7rr9ybj", "Integrity": "oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\responsive.js", "FileLength": 11780, "LastWriteTime": "2025-07-10T01:47:42+00:00"}], "Endpoints": [{"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "508754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-1xiwpnv0y9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018156070"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55077"}, {"Name": "ETag", "Value": "\"9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-1xiwpnv0y9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44027"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-rftke82za3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000103029054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "ETag", "Value": "\"X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-rftke82za3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css.gz"}]}, {"Route": "css/blazor-diagrams.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "css/blazor-diagrams.min.css"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "css/blazor-diagrams.min.css"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "css/blazor-diagrams.min.css.gz"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041925205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041925205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000474608448"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000474608448"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076528660"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "W/\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000074266617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "W/\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg.gz"}, {"Name": "integrity", "Value": "sha256-27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000074266617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "W/\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076528660"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "W/\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf.gz"}, {"Name": "integrity", "Value": "sha256-hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000673400673"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "label", "Value": "css/open-iconic/README.md"}, {"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "label", "Value": "css/open-iconic/README.md"}, {"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "label", "Value": "css/open-iconic/README.md.gz"}, {"Name": "integrity", "Value": "sha256-cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q="}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000673400673"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.md.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q="}]}, {"Route": "css/site.cp57924o9j.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-cp57924o9j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000447027269"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "ETag", "Value": "\"Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:17:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cp57924o9j"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0="}]}, {"Route": "css/site.cp57924o9j.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4929"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:14:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cp57924o9j"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0="}]}, {"Route": "css/site.cp57924o9j.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-cp57924o9j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:17:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cp57924o9j"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0="}]}, {"Route": "css/site.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-cp57924o9j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000447027269"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "ETag", "Value": "\"Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:17:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0="}]}, {"Route": "css/site.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4929"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:14:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YLbXTKiKf5HPay05+aE0Xtk/zTrTtBeWOaMQzd1dBz0="}]}, {"Route": "css/site.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-cp57924o9j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2236"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 02:17:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hyn8mnT8rfI73WEs/K6x3iFSAQFQyk5mIq97h0jTus0="}]}, {"Route": "DiagramEditor.1b9liozj85.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001059322034"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "943"}, {"Name": "ETag", "Value": "\"wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b9liozj85"}, {"Name": "label", "Value": "DiagramEditor.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8="}]}, {"Route": "DiagramEditor.1b9liozj85.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3017"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b9liozj85"}, {"Name": "label", "Value": "DiagramEditor.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8="}]}, {"Route": "DiagramEditor.1b9liozj85.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "943"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b9liozj85"}, {"Name": "label", "Value": "DiagramEditor.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU="}]}, {"Route": "DiagramEditor.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001059322034"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "943"}, {"Name": "ETag", "Value": "\"wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8="}]}, {"Route": "DiagramEditor.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3017"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ozsq9k4iP+enFVWPHLzCHz3XsGwUf12683fP81pA7e8="}]}, {"Route": "DiagramEditor.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "943"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU="}]}, {"Route": "DiagramEditor.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001020408163"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}]}, {"Route": "DiagramEditor.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}]}, {"Route": "DiagramEditor.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI="}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001020408163"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}, {"Name": "label", "Value": "DiagramEditor.styles.css"}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}, {"Name": "label", "Value": "DiagramEditor.styles.css"}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "label", "Value": "DiagramEditor.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/blazor-diagrams.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "js/blazor-diagrams.min.js"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "js/blazor-diagrams.min.js"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "js/blazor-diagrams.min.js.gz"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000582411182"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "label", "Value": "js/diagram-export.js"}, {"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "label", "Value": "js/diagram-export.js"}, {"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "label", "Value": "js/diagram-export.js.gz"}, {"Name": "integrity", "Value": "sha256-tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o="}]}, {"Route": "js/diagram-export.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000582411182"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o="}]}, {"Route": "js/responsive.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000343170899"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:47:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8="}]}, {"Route": "js/responsive.wkr7rr9ybj.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000343170899"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "label", "Value": "js/responsive.js"}, {"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.wkr7rr9ybj.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:47:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "label", "Value": "js/responsive.js"}, {"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.wkr7rr9ybj.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "label", "Value": "js/responsive.js.gz"}, {"Name": "integrity", "Value": "sha256-5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8="}]}]}